import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { mentionNotificationService } from '@/services/mentionNotificationService';

/**
 * POST API webhook for message creation
 * 
 * This endpoint is called by the socket server when a new message is created
 * to process mentions and send notifications.
 * 
 * Request body:
 * {
 *   "messageId": 123,
 *   "chatId": 456,
 *   "userId": 789,
 *   "content": "Message content with @mentions",
 *   "chatType": "private" | "task" | "department" | "organization"
 * }
 */
export async function POST(request: NextRequest) {
  try {
    // Verify webhook authenticity (optional - add webhook secret verification)
    const webhookSecret = request.headers.get('x-webhook-secret');
    const expectedSecret = process.env.WEBHOOK_SECRET;
    
    if (expectedSecret && webhookSecret !== expectedSecret) {
      return NextResponse.json(
        { error: 'Unauthorized webhook request' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { messageId, chatId, userId, content, chatType } = body;

    // Validate required fields
    if (!messageId || !chatId || !userId || !content || !chatType) {
      return NextResponse.json(
        { error: 'Missing required fields: messageId, chatId, userId, content, chatType' },
        { status: 400 }
      );
    }

    // Get message details from database
    const message = await prisma.chatMessage.findUnique({
      where: { id: messageId },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        chat: {
          select: {
            id: true,
            name: true,
            chatType: true,
          },
        },
      },
    });

    if (!message) {
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      );
    }

    // Process mentions in the message content
    try {
      const senderName = `${message.user.firstName} ${message.user.lastName}`;
      const chatName = message.chat.name || `${message.chat.chatType} Chat`;
      
      const notifications = await mentionNotificationService.processMentionsInMessage(
        message.id,
        message.content,
        message.chatId,
        message.chat.chatType,
        chatName,
        message.userId,
        senderName
      );

      return NextResponse.json({
        message: 'Mentions processed successfully',
        notificationsCreated: notifications.length,
      });
    } catch (mentionError) {
      console.error('Error processing mentions:', mentionError);
      return NextResponse.json(
        { error: 'Failed to process mentions' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in message created webhook:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
