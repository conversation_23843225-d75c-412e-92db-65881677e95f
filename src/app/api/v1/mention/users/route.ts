import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/utils/auth';
import { prisma } from '@/lib/prisma';

/**
 * GET API for mention user search
 * 
 * This endpoint returns users that can be mentioned in a specific chat context.
 * It filters users based on chat type and user permissions.
 * 
 * Query parameters:
 * - chatId: Required. ID of the chat
 * - chatType: Required. Type of chat ('private', 'task', 'department', 'organization')
 * - query: Optional. Search query for filtering users by name or email
 * - limit: Optional. Maximum number of users to return (default: 10)
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const { searchParams } = new URL(request.url);
    const chatId = searchParams.get('chatId');
    const chatType = searchParams.get('chatType') as 'private' | 'task' | 'department' | 'organization';
    const query = searchParams.get('query');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Validate required parameters
    if (!chatId || !chatType) {
      return NextResponse.json(
        { error: 'chatId and chatType are required' },
        { status: 400 }
      );
    }

    // Validate chat type
    if (!['private', 'task', 'department', 'organization'].includes(chatType)) {
      return NextResponse.json(
        { error: 'Invalid chat type' },
        { status: 400 }
      );
    }

    let users: any[] = [];

    try {
      switch (chatType) {
        case 'private':
          users = await getPrivateChatUsers(parseInt(chatId), auth.userId, query, limit);
          break;
        case 'task':
          users = await getTaskChatUsers(parseInt(chatId), auth.userId, query, limit);
          break;
        case 'department':
          users = await getDepartmentChatUsers(parseInt(chatId), auth.userId, query, limit);
          break;
        case 'organization':
          users = await getOrganizationChatUsers(parseInt(chatId), auth.userId, query, limit);
          break;
      }
    } catch (error) {
      console.error('Error fetching mention users:', error);
      return NextResponse.json(
        { error: 'Failed to fetch users for mentions' },
        { status: 500 }
      );
    }

    // Format users for mention dropdown
    const mentionUsers = users.map(user => ({
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      imageUrl: user.imageUrl,
      isOnline: false, // TODO: Implement online status
    }));

    return NextResponse.json({ users: mentionUsers });
  } catch (error) {
    console.error('Error in mention users API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to get users for private chat mentions
async function getPrivateChatUsers(chatId: number, currentUserId: number, query?: string, limit: number = 10) {
  const nameFilter = query ? {
    OR: [
      { firstName: { contains: query, mode: 'insensitive' as const } },
      { lastName: { contains: query, mode: 'insensitive' as const } },
      { email: { contains: query, mode: 'insensitive' as const } },
    ],
  } : undefined;

  // Get users in the private chat
  const chatUsers = await prisma.chatUser.findMany({
    where: {
      chatId,
      user: {
        deletedAt: null,
        ...(nameFilter && nameFilter),
      },
    },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          imageUrl: true,
        },
      },
    },
    take: limit,
    orderBy: [
      { user: { firstName: 'asc' } },
      { user: { lastName: 'asc' } },
    ],
  });

  return chatUsers.map(cu => cu.user).filter(user => user.id !== currentUserId);
}

// Helper function to get users for task chat mentions
async function getTaskChatUsers(chatId: number, currentUserId: number, query?: string, limit: number = 10) {
  const nameFilter = query ? {
    OR: [
      { firstName: { contains: query, mode: 'insensitive' as const } },
      { lastName: { contains: query, mode: 'insensitive' as const } },
      { email: { contains: query, mode: 'insensitive' as const } },
    ],
  } : undefined;

  // Get the task associated with this chat
  const chat = await prisma.chat.findUnique({
    where: { id: chatId },
    include: { task: true },
  });

  if (!chat?.task) {
    return [];
  }

  // Get task assignees and creator
  const taskUsers = await prisma.user.findMany({
    where: {
      OR: [
        { id: chat.task.createdByUserId },
        { id: chat.task.assignedToUserId },
      ],
      deletedAt: null,
      ...(nameFilter && nameFilter),
    },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true,
      imageUrl: true,
    },
    take: limit,
    orderBy: [
      { firstName: 'asc' },
      { lastName: 'asc' },
    ],
  });

  return taskUsers.filter(user => user.id !== currentUserId);
}

// Helper function to get users for department chat mentions
async function getDepartmentChatUsers(chatId: number, currentUserId: number, query?: string, limit: number = 10) {
  const nameFilter = query ? {
    OR: [
      { firstName: { contains: query, mode: 'insensitive' as const } },
      { lastName: { contains: query, mode: 'insensitive' as const } },
      { email: { contains: query, mode: 'insensitive' as const } },
    ],
  } : undefined;

  // Get the department associated with this chat
  const chat = await prisma.chat.findUnique({
    where: { id: chatId },
    include: { department: true },
  });

  if (!chat?.department) {
    return [];
  }

  // Get department members
  const departmentMembers = await prisma.departmentMember.findMany({
    where: {
      departmentId: chat.department.id,
      user: {
        deletedAt: null,
        ...(nameFilter && nameFilter),
      },
    },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          imageUrl: true,
        },
      },
    },
    take: limit,
    orderBy: [
      { user: { firstName: 'asc' } },
      { user: { lastName: 'asc' } },
    ],
  });

  return departmentMembers.map(dm => dm.user).filter(user => user.id !== currentUserId);
}

// Helper function to get users for organization chat mentions
async function getOrganizationChatUsers(chatId: number, currentUserId: number, query?: string, limit: number = 10) {
  const nameFilter = query ? {
    OR: [
      { firstName: { contains: query, mode: 'insensitive' as const } },
      { lastName: { contains: query, mode: 'insensitive' as const } },
      { email: { contains: query, mode: 'insensitive' as const } },
    ],
  } : undefined;

  // Get the organization associated with this chat
  const chat = await prisma.chat.findUnique({
    where: { id: chatId },
    include: { organization: true },
  });

  if (!chat?.organization) {
    return [];
  }

  const organizationId = chat.organization.id;
  const users: any[] = [];

  // Get organization owner
  if (chat.organization.ownerId !== currentUserId) {
    const owner = await prisma.user.findUnique({
      where: {
        id: chat.organization.ownerId,
        deletedAt: null,
        ...(nameFilter && nameFilter),
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        imageUrl: true,
      },
    });
    if (owner) users.push(owner);
  }

  // Get organization admins
  const orgAdmins = await prisma.organizationAdmin.findMany({
    where: {
      organizationId,
      isActive: true,
      user: {
        id: { not: currentUserId },
        deletedAt: null,
        ...(nameFilter && nameFilter),
      },
    },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          imageUrl: true,
        },
      },
    },
  });

  users.push(...orgAdmins.map(admin => admin.user));

  // Get department members from all departments in the organization
  const departmentMembers = await prisma.departmentMember.findMany({
    where: {
      department: { organizationId },
      user: {
        id: { not: currentUserId },
        deletedAt: null,
        ...(nameFilter && nameFilter),
      },
    },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          imageUrl: true,
        },
      },
    },
  });

  users.push(...departmentMembers.map(member => member.user));

  // Remove duplicates and limit results
  const uniqueUsers = users.filter((user, index, self) => 
    index === self.findIndex(u => u.id === user.id)
  );

  return uniqueUsers
    .sort((a, b) => `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`))
    .slice(0, limit);
}
