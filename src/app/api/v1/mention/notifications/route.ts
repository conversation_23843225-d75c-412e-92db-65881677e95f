import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/utils/auth';
import { mentionNotificationService, mentionNotificationUtils } from '@/services/mentionNotificationService';

/**
 * GET API for mention notifications
 * 
 * This endpoint returns mention notifications for the authenticated user.
 * 
 * Query parameters:
 * - limit: Optional. Maximum number of notifications to return (default: 20)
 * - unreadOnly: Optional. If true, returns only unread notifications
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const unreadOnly = searchParams.get('unreadOnly') === 'true';

    // Get mention notifications
    const notifications = await mentionNotificationService.getUserMentionNotifications(
      auth.userId,
      limit
    );

    // Filter for unread only if requested
    const filteredNotifications = unreadOnly 
      ? notifications.filter(n => !n.isRead)
      : notifications;

    // Format notifications for response
    const formattedNotifications = filteredNotifications.map(userNotification => ({
      id: userNotification.id,
      notificationId: userNotification.notificationId,
      isRead: userNotification.isRead,
      readAt: userNotification.readAt,
      createdAt: userNotification.createdAt,
      ...mentionNotificationUtils.formatNotificationContent(userNotification.notification),
    }));

    // Get unread count
    const unreadCount = await mentionNotificationService.getUnreadMentionCount(auth.userId);

    return NextResponse.json({
      notifications: formattedNotifications,
      unreadCount,
      total: notifications.length,
    });
  } catch (error) {
    console.error('Error fetching mention notifications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch mention notifications' },
      { status: 500 }
    );
  }
}

/**
 * PATCH API to mark mention notifications as read
 * 
 * Body parameters:
 * - notificationId: Required. ID of the notification to mark as read
 * - markAllAsRead: Optional. If true, marks all mention notifications as read
 */
export async function PATCH(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { notificationId, markAllAsRead } = body;

    if (markAllAsRead) {
      // Mark all mention notifications as read for the user
      const notifications = await mentionNotificationService.getUserMentionNotifications(
        auth.userId,
        1000 // Large number to get all notifications
      );

      const updatePromises = notifications
        .filter(n => !n.isRead)
        .map(n => 
          mentionNotificationService.markMentionNotificationAsRead(
            auth.userId,
            n.notificationId
          )
        );

      await Promise.all(updatePromises);

      return NextResponse.json({
        message: 'All mention notifications marked as read',
        updatedCount: updatePromises.length,
      });
    } else if (notificationId) {
      // Mark specific notification as read
      await mentionNotificationService.markMentionNotificationAsRead(
        auth.userId,
        notificationId
      );

      return NextResponse.json({
        message: 'Mention notification marked as read',
      });
    } else {
      return NextResponse.json(
        { error: 'notificationId or markAllAsRead is required' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error updating mention notifications:', error);
    return NextResponse.json(
      { error: 'Failed to update mention notifications' },
      { status: 500 }
    );
  }
}
