import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/utils/auth';
import { prisma } from '@/lib/prisma';

/**
 * GET API to get user details for mention display
 * 
 * This endpoint returns basic user information for displaying mentions
 * in chat messages or for user profile previews.
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const { id } = await params;
    const userId = parseInt(id);
    
    if (isNaN(userId)) {
      return NextResponse.json(
        { error: 'Invalid user ID' },
        { status: 400 }
      );
    }

    // Get user details
    const user = await prisma.user.findUnique({
      where: {
        id: userId,
        deletedAt: null, // Only active users
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        imageUrl: true,
        userRole: {
          select: {
            id: true,
            name: true,
            isOwner: true,
            isAdmin: true,
            isMember: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Format user for mention display
    const mentionUser = {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      imageUrl: user.imageUrl,
      isOnline: false, // TODO: Implement online status
      role: user.userRole?.name,
    };

    return NextResponse.json({ user: mentionUser });
  } catch (error) {
    console.error('Error fetching mention user details:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
