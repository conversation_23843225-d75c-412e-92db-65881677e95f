'use client';

import React, { useState } from 'react';
import styled from 'styled-components';
import { appTheme } from '@/app/theme';
import MentionText, { MentionData, parseMentions } from './MentionText';
import { mentionApi } from '@/services/mentionService';
import { toast } from 'react-hot-toast';

interface MessageWithMentionsProps {
  content: string;
  isInOwnMessage?: boolean;
  onMentionClick?: (mention: MentionData) => void;
}

const MessageContainer = styled.div`
  line-height: 1.5;
  word-wrap: break-word;
  overflow-wrap: break-word;
`;

const TextSpan = styled.span`
  white-space: pre-wrap;
`;

// User Profile Modal Components
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
`;

const ModalContent = styled.div`
  background: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.lg};
  padding: ${appTheme.spacing.xl};
  box-shadow: ${appTheme.shadows.lg};
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
`;

const ModalHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.md};
  margin-bottom: ${appTheme.spacing.lg};
`;

const UserAvatar = styled.div<{ $imageUrl?: string }>`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 600;
  color: white;
  background: ${props =>
    props.$imageUrl && props.$imageUrl.trim() !== ''
      ? `url(${props.$imageUrl})`
      : `linear-gradient(135deg, ${appTheme.colors.primary} 0%, #667eea 100%)`};
  background-size: cover;
  background-position: center;
  border: 3px solid white;
  box-shadow: ${appTheme.shadows.md};
`;

const UserInfo = styled.div`
  flex: 1;
`;

const UserName = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: ${appTheme.colors.text.primary};
  margin: 0 0 ${appTheme.spacing.xs} 0;
`;

const UserEmail = styled.div`
  font-size: 14px;
  color: ${appTheme.colors.text.secondary};
  margin-bottom: ${appTheme.spacing.xs};
`;

const UserRole = styled.div`
  font-size: 12px;
  color: ${appTheme.colors.primary};
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const CloseButton = styled.button`
  position: absolute;
  top: ${appTheme.spacing.md};
  right: ${appTheme.spacing.md};
  background: none;
  border: none;
  font-size: 24px;
  color: ${appTheme.colors.text.secondary};
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;

  &:hover {
    background: ${appTheme.colors.background.lighter};
    color: ${appTheme.colors.text.primary};
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.xl};
  color: ${appTheme.colors.text.secondary};
`;

const LoadingSpinner = styled.div`
  width: 24px;
  height: 24px;
  border: 2px solid ${appTheme.colors.border};
  border-top: 2px solid ${appTheme.colors.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: ${appTheme.spacing.sm};

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

interface UserProfileModalProps {
  userId: number;
  onClose: () => void;
}

function UserProfileModal({ userId, onClose }: UserProfileModalProps) {
  const [userDetails, setUserDetails] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  React.useEffect(() => {
    const fetchUserDetails = async () => {
      try {
        const response = await mentionApi.getUser(userId);
        setUserDetails(response.user);
      } catch (error) {
        console.error('Error fetching user details:', error);
        toast.error('Failed to load user details');
        onClose();
      } finally {
        setLoading(false);
      }
    };

    fetchUserDetails();
  }, [userId, onClose]);

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const getUserInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const shouldShowUserImage = (imageUrl?: string) => {
    return imageUrl && imageUrl.trim() !== '' && imageUrl !== 'null' && imageUrl !== 'undefined';
  };

  return (
    <ModalOverlay onClick={handleOverlayClick}>
      <ModalContent>
        <CloseButton onClick={onClose}>×</CloseButton>
        
        {loading ? (
          <LoadingContainer>
            <LoadingSpinner />
            Loading user details...
          </LoadingContainer>
        ) : userDetails ? (
          <>
            <ModalHeader>
              <UserAvatar
                $imageUrl={shouldShowUserImage(userDetails.imageUrl) ? userDetails.imageUrl : undefined}
              >
                {!shouldShowUserImage(userDetails.imageUrl) &&
                  getUserInitials(userDetails.firstName, userDetails.lastName)}
              </UserAvatar>
              <UserInfo>
                <UserName>{userDetails.firstName} {userDetails.lastName}</UserName>
                <UserEmail>{userDetails.email}</UserEmail>
                {userDetails.role && <UserRole>{userDetails.role}</UserRole>}
              </UserInfo>
            </ModalHeader>
          </>
        ) : (
          <div>User not found</div>
        )}
      </ModalContent>
    </ModalOverlay>
  );
}

export default function MessageWithMentions({
  content,
  isInOwnMessage = false,
  onMentionClick
}: MessageWithMentionsProps) {
  const [showUserModal, setShowUserModal] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);

  const handleMentionClick = (mention: MentionData) => {
    if (onMentionClick) {
      onMentionClick(mention);
    } else {
      // Default behavior: show user profile modal
      setSelectedUserId(mention.id);
      setShowUserModal(true);
    }
  };

  const handleCloseModal = () => {
    setShowUserModal(false);
    setSelectedUserId(null);
  };

  // Parse the content to identify mentions
  const parsedContent = parseMentions(content);

  return (
    <>
      <MessageContainer>
        {parsedContent.map((part, index) => {
          if (part.type === 'mention' && part.mention) {
            return (
              <MentionText
                key={index}
                mention={part.mention}
                onClick={handleMentionClick}
                isInOwnMessage={isInOwnMessage}
              />
            );
          } else {
            return <TextSpan key={index}>{part.content}</TextSpan>;
          }
        })}
      </MessageContainer>

      {/* User Profile Modal */}
      {showUserModal && selectedUserId && (
        <UserProfileModal
          userId={selectedUserId}
          onClose={handleCloseModal}
        />
      )}
    </>
  );
}
