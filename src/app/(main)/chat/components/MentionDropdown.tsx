'use client';

import React, { useEffect, useRef } from 'react';
import styled from 'styled-components';
import { appTheme } from '@/app/theme';

export interface MentionUser {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  imageUrl?: string;
  isOnline?: boolean;
}

interface MentionDropdownProps {
  users: MentionUser[];
  selectedIndex: number;
  onSelect: (user: MentionUser) => void;
  onClose: () => void;
  position: { top: number; left: number };
  loading?: boolean;
}

const DropdownContainer = styled.div<{ $top: number; $left: number }>`
  position: fixed;
  top: ${props => props.$top}px;
  left: ${props => props.$left}px;
  background: ${appTheme.colors.background.main};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.lg};
  box-shadow: ${appTheme.shadows.lg};
  z-index: 1000;
  max-height: 200px;
  min-width: 250px;
  overflow-y: auto;
  backdrop-filter: blur(10px);

  /* Enhanced scrollbar styling */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: ${appTheme.colors.border};
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: ${appTheme.colors.text.light};
  }
`;

const MentionItem = styled.div<{ $selected: boolean }>`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  cursor: pointer;
  transition: all 0.2s ease;
  background: ${props => props.$selected ? appTheme.colors.primaryLight : 'transparent'};
  border-radius: ${appTheme.borderRadius.sm};
  margin: ${appTheme.spacing.xs};

  &:hover {
    background: ${props => props.$selected ? appTheme.colors.primaryLight : appTheme.colors.background.lighter};
  }

  &:first-child {
    margin-top: ${appTheme.spacing.sm};
  }

  &:last-child {
    margin-bottom: ${appTheme.spacing.sm};
  }
`;

const UserAvatar = styled.div<{ $imageUrl?: string }>`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: white;
  flex-shrink: 0;
  background: ${props =>
    props.$imageUrl && props.$imageUrl.trim() !== ''
      ? `url(${props.$imageUrl})`
      : `linear-gradient(135deg, ${appTheme.colors.primary} 0%, #667eea 100%)`};
  background-size: cover;
  background-position: center;
  border: 2px solid white;
  box-shadow: ${appTheme.shadows.sm};
  position: relative;

  /* Online indicator */
  &::after {
    content: '';
    position: absolute;
    bottom: -1px;
    right: -1px;
    width: 10px;
    height: 10px;
    background: #10b981;
    border: 2px solid white;
    border-radius: 50%;
    opacity: ${props => props.$imageUrl ? 1 : 0};
    transition: opacity 0.2s ease;
  }
`;

const UserInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const UserName = styled.div`
  font-size: 14px;
  font-weight: 500;
  color: ${appTheme.colors.text.primary};
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const UserEmail = styled.div`
  font-size: 12px;
  color: ${appTheme.colors.text.secondary};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const LoadingContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.lg};
  color: ${appTheme.colors.text.secondary};
  font-size: 14px;
`;

const EmptyState = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.lg};
  color: ${appTheme.colors.text.secondary};
  font-size: 14px;
  text-align: center;
`;

const LoadingSpinner = styled.div`
  width: 16px;
  height: 16px;
  border: 2px solid ${appTheme.colors.border};
  border-top: 2px solid ${appTheme.colors.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: ${appTheme.spacing.sm};

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

export default function MentionDropdown({
  users,
  selectedIndex,
  onSelect,
  onClose,
  position,
  loading = false
}: MentionDropdownProps) {
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Handle clicks outside dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  // Scroll selected item into view
  useEffect(() => {
    if (dropdownRef.current && selectedIndex >= 0) {
      const selectedElement = dropdownRef.current.children[selectedIndex + (loading ? 1 : 0)] as HTMLElement;
      if (selectedElement) {
        selectedElement.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth'
        });
      }
    }
  }, [selectedIndex, loading]);

  const getUserInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const shouldShowUserImage = (imageUrl?: string) => {
    return imageUrl && imageUrl.trim() !== '' && imageUrl !== 'null' && imageUrl !== 'undefined';
  };

  return (
    <DropdownContainer
      ref={dropdownRef}
      $top={position.top}
      $left={position.left}
    >
      {loading && (
        <LoadingContainer>
          <LoadingSpinner />
          Searching users...
        </LoadingContainer>
      )}
      
      {!loading && users.length === 0 && (
        <EmptyState>
          No users found
        </EmptyState>
      )}

      {!loading && users.map((user, index) => (
        <MentionItem
          key={user.id}
          $selected={index === selectedIndex}
          onClick={() => onSelect(user)}
        >
          <UserAvatar
            $imageUrl={shouldShowUserImage(user.imageUrl) ? user.imageUrl : undefined}
            title={`${user.firstName} ${user.lastName}`}
          >
            {!shouldShowUserImage(user.imageUrl) &&
              getUserInitials(user.firstName, user.lastName)}
          </UserAvatar>
          <UserInfo>
            <UserName>{user.firstName} {user.lastName}</UserName>
            <UserEmail>{user.email}</UserEmail>
          </UserInfo>
        </MentionItem>
      ))}
    </DropdownContainer>
  );
}
