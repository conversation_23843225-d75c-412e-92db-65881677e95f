'use client';

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Bell, X, MessageCircle } from 'lucide-react';
import { appTheme } from '@/app/theme';
import { toast } from 'react-hot-toast';

interface MentionNotification {
  id: number;
  notificationId: number;
  isRead: boolean;
  readAt: string | null;
  createdAt: string;
  title: string;
  content: string;
  senderName: string;
  chatName: string;
  messagePreview: string;
  chatId: number;
  chatType: string;
  messageId: number;
}

interface MentionNotificationsProps {
  onNavigateToChat?: (chatId: number, chatType: string, messageId?: number) => void;
}

const NotificationButton = styled.button<{ $hasUnread: boolean }>`
  position: relative;
  padding: ${appTheme.spacing.sm};
  border: none;
  background: none;
  color: ${appTheme.colors.text.secondary};
  cursor: pointer;
  border-radius: ${appTheme.borderRadius.sm};
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  min-height: 36px;

  &:hover {
    background: ${appTheme.colors.background.lighter};
    color: ${appTheme.colors.text.primary};
  }

  ${props => props.$hasUnread && `
    color: ${appTheme.colors.primary};
    
    &::after {
      content: '';
      position: absolute;
      top: 6px;
      right: 6px;
      width: 8px;
      height: 8px;
      background: ${appTheme.colors.error.main};
      border-radius: 50%;
      border: 2px solid ${appTheme.colors.background.main};
    }
  `}
`;

const NotificationDropdown = styled.div<{ $isOpen: boolean }>`
  position: absolute;
  top: 100%;
  right: 0;
  background: ${appTheme.colors.background.main};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.lg};
  box-shadow: ${appTheme.shadows.lg};
  z-index: 1000;
  min-width: 350px;
  max-width: 400px;
  max-height: 400px;
  overflow-y: auto;
  backdrop-filter: blur(10px);
  display: ${props => props.$isOpen ? 'block' : 'none'};
  margin-top: ${appTheme.spacing.xs};

  /* Enhanced scrollbar styling */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: ${appTheme.colors.border};
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: ${appTheme.colors.text.light};
  }
`;

const NotificationHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${appTheme.spacing.md};
  border-bottom: 1px solid ${appTheme.colors.border};
  background: ${appTheme.colors.background.light};
  border-radius: ${appTheme.borderRadius.lg} ${appTheme.borderRadius.lg} 0 0;
`;

const NotificationTitle = styled.h3`
  font-size: 16px;
  font-weight: 600;
  color: ${appTheme.colors.text.primary};
  margin: 0;
`;

const MarkAllReadButton = styled.button`
  font-size: 12px;
  color: ${appTheme.colors.primary};
  background: none;
  border: none;
  cursor: pointer;
  padding: ${appTheme.spacing.xs};
  border-radius: ${appTheme.borderRadius.sm};
  transition: all 0.2s ease;

  &:hover {
    background: ${appTheme.colors.primaryLight};
  }
`;

const NotificationItem = styled.div<{ $isRead: boolean }>`
  padding: ${appTheme.spacing.md};
  border-bottom: 1px solid ${appTheme.colors.border};
  cursor: pointer;
  transition: all 0.2s ease;
  background: ${props => props.$isRead ? 'transparent' : appTheme.colors.primaryLight};
  opacity: ${props => props.$isRead ? 0.8 : 1};

  &:hover {
    background: ${appTheme.colors.background.lighter};
  }

  &:last-child {
    border-bottom: none;
    border-radius: 0 0 ${appTheme.borderRadius.lg} ${appTheme.borderRadius.lg};
  }
`;

const NotificationContent = styled.div`
  display: flex;
  align-items: flex-start;
  gap: ${appTheme.spacing.sm};
`;

const NotificationIcon = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: ${appTheme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
`;

const NotificationText = styled.div`
  flex: 1;
  min-width: 0;
`;

const NotificationMessage = styled.div`
  font-size: 14px;
  font-weight: 500;
  color: ${appTheme.colors.text.primary};
  margin-bottom: ${appTheme.spacing.xs};
  line-height: 1.4;
`;

const NotificationPreview = styled.div`
  font-size: 12px;
  color: ${appTheme.colors.text.secondary};
  margin-bottom: ${appTheme.spacing.xs};
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const NotificationTime = styled.div`
  font-size: 11px;
  color: ${appTheme.colors.text.light};
`;

const EmptyState = styled.div`
  padding: ${appTheme.spacing.xl};
  text-align: center;
  color: ${appTheme.colors.text.secondary};
`;

const LoadingState = styled.div`
  padding: ${appTheme.spacing.lg};
  text-align: center;
  color: ${appTheme.colors.text.secondary};
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${appTheme.spacing.sm};
`;

const LoadingSpinner = styled.div`
  width: 16px;
  height: 16px;
  border: 2px solid ${appTheme.colors.border};
  border-top: 2px solid ${appTheme.colors.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

export default function MentionNotifications({ onNavigateToChat }: MentionNotificationsProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<MentionNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);

  // Fetch notifications
  const fetchNotifications = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/mention/notifications');
      if (response.ok) {
        const data = await response.json();
        setNotifications(data.notifications);
        setUnreadCount(data.unreadCount);
      }
    } catch (error) {
      console.error('Error fetching mention notifications:', error);
      toast.error('Failed to load notifications');
    } finally {
      setLoading(false);
    }
  };

  // Mark notification as read
  const markAsRead = async (notificationId: number) => {
    try {
      const response = await fetch('/api/v1/mention/notifications', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ notificationId }),
      });

      if (response.ok) {
        setNotifications(prev =>
          prev.map(n =>
            n.notificationId === notificationId
              ? { ...n, isRead: true, readAt: new Date().toISOString() }
              : n
          )
        );
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Mark all as read
  const markAllAsRead = async () => {
    try {
      const response = await fetch('/api/v1/mention/notifications', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ markAllAsRead: true }),
      });

      if (response.ok) {
        setNotifications(prev =>
          prev.map(n => ({ ...n, isRead: true, readAt: new Date().toISOString() }))
        );
        setUnreadCount(0);
        toast.success('All notifications marked as read');
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      toast.error('Failed to mark notifications as read');
    }
  };

  // Handle notification click
  const handleNotificationClick = (notification: MentionNotification) => {
    if (!notification.isRead) {
      markAsRead(notification.notificationId);
    }

    if (onNavigateToChat) {
      onNavigateToChat(notification.chatId, notification.chatType, notification.messageId);
    }

    setIsOpen(false);
  };

  // Format time
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  // Fetch notifications when opened
  useEffect(() => {
    if (isOpen) {
      fetchNotifications();
    }
  }, [isOpen]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('[data-notification-dropdown]')) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  return (
    <div style={{ position: 'relative' }} data-notification-dropdown>
      <NotificationButton
        $hasUnread={unreadCount > 0}
        onClick={() => setIsOpen(!isOpen)}
        title={`Mentions (${unreadCount} unread)`}
      >
        <Bell size={18} />
      </NotificationButton>

      <NotificationDropdown $isOpen={isOpen}>
        <NotificationHeader>
          <NotificationTitle>Mentions</NotificationTitle>
          {unreadCount > 0 && (
            <MarkAllReadButton onClick={markAllAsRead}>
              Mark all read
            </MarkAllReadButton>
          )}
        </NotificationHeader>

        {loading ? (
          <LoadingState>
            <LoadingSpinner />
            Loading...
          </LoadingState>
        ) : notifications.length === 0 ? (
          <EmptyState>
            No mention notifications
          </EmptyState>
        ) : (
          notifications.map(notification => (
            <NotificationItem
              key={notification.id}
              $isRead={notification.isRead}
              onClick={() => handleNotificationClick(notification)}
            >
              <NotificationContent>
                <NotificationIcon>
                  <MessageCircle size={16} />
                </NotificationIcon>
                <NotificationText>
                  <NotificationMessage>
                    {notification.senderName} mentioned you in {notification.chatName}
                  </NotificationMessage>
                  <NotificationPreview>
                    {notification.messagePreview}
                  </NotificationPreview>
                  <NotificationTime>
                    {formatTime(notification.createdAt)}
                  </NotificationTime>
                </NotificationText>
              </NotificationContent>
            </NotificationItem>
          ))
        )}
      </NotificationDropdown>
    </div>
  );
}
