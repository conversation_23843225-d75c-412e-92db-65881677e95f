'use client';

import React from 'react';
import styled from 'styled-components';
import { appTheme } from '@/app/theme';

export interface MentionData {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
}

interface MentionTextProps {
  mention: MentionData;
  onClick?: (mention: MentionData) => void;
  isInOwnMessage?: boolean;
}

const MentionSpan = styled.span<{ $isInOwnMessage?: boolean; $clickable?: boolean }>`
  background: ${props => 
    props.$isInOwnMessage 
      ? 'rgba(255, 255, 255, 0.2)' 
      : appTheme.colors.primaryLight
  };
  color: ${props => 
    props.$isInOwnMessage 
      ? 'white' 
      : appTheme.colors.primary
  };
  padding: 2px 6px;
  border-radius: ${appTheme.borderRadius.sm};
  font-weight: 600;
  cursor: ${props => props.$clickable ? 'pointer' : 'default'};
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  border: 1px solid ${props => 
    props.$isInOwnMessage 
      ? 'rgba(255, 255, 255, 0.3)' 
      : appTheme.colors.primary
  };

  &:hover {
    ${props => props.$clickable && `
      background: ${props.$isInOwnMessage 
        ? 'rgba(255, 255, 255, 0.3)' 
        : appTheme.colors.primary
      };
      color: ${props.$isInOwnMessage 
        ? 'white' 
        : 'white'
      };
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    `}
  }

  &:active {
    ${props => props.$clickable && `
      transform: translateY(0);
    `}
  }
`;

const MentionIcon = styled.span`
  font-size: 12px;
  opacity: 0.8;
`;

export default function MentionText({ 
  mention, 
  onClick, 
  isInOwnMessage = false 
}: MentionTextProps) {
  const handleClick = (e: React.MouseEvent) => {
    if (onClick) {
      e.preventDefault();
      e.stopPropagation();
      onClick(mention);
    }
  };

  return (
    <MentionSpan
      $isInOwnMessage={isInOwnMessage}
      $clickable={!!onClick}
      onClick={handleClick}
      title={`${mention.firstName} ${mention.lastName} (${mention.email})`}
    >
      <MentionIcon>@</MentionIcon>
      {mention.firstName} {mention.lastName}
    </MentionSpan>
  );
}

// Utility function to parse mentions from text
export const parseMentions = (text: string): Array<{ type: 'text' | 'mention'; content: string; mention?: MentionData }> => {
  const mentionRegex = /@\[([^\]]+)\]\((\d+)\)/g;
  const parts: Array<{ type: 'text' | 'mention'; content: string; mention?: MentionData }> = [];
  let lastIndex = 0;
  let match;

  while ((match = mentionRegex.exec(text)) !== null) {
    // Add text before mention
    if (match.index > lastIndex) {
      parts.push({
        type: 'text',
        content: text.slice(lastIndex, match.index)
      });
    }

    // Parse mention data
    const mentionText = match[1];
    const userId = parseInt(match[2]);
    
    // Extract name and email from mention text (format: "First Last (email)")
    const nameEmailMatch = mentionText.match(/^(.+?)\s*\(([^)]+)\)$/);
    if (nameEmailMatch) {
      const fullName = nameEmailMatch[1].trim();
      const email = nameEmailMatch[2].trim();
      const nameParts = fullName.split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      parts.push({
        type: 'mention',
        content: match[0],
        mention: {
          id: userId,
          firstName,
          lastName,
          email
        }
      });
    } else {
      // Fallback: treat as regular text if parsing fails
      parts.push({
        type: 'text',
        content: match[0]
      });
    }

    lastIndex = match.index + match[0].length;
  }

  // Add remaining text
  if (lastIndex < text.length) {
    parts.push({
      type: 'text',
      content: text.slice(lastIndex)
    });
  }

  return parts;
};

// Utility function to create mention markup
export const createMentionMarkup = (user: MentionData): string => {
  return `@[${user.firstName} ${user.lastName} (${user.email})](${user.id})`;
};

// Utility function to extract plain text from content with mentions
export const extractPlainText = (content: string): string => {
  return content.replace(/@\[([^\]]+)\]\(\d+\)/g, '@$1');
};

// Utility function to get mentioned user IDs from content
export const getMentionedUserIds = (content: string): number[] => {
  const mentionRegex = /@\[([^\]]+)\]\((\d+)\)/g;
  const userIds: number[] = [];
  let match;

  while ((match = mentionRegex.exec(content)) !== null) {
    const userId = parseInt(match[2]);
    if (!isNaN(userId) && !userIds.includes(userId)) {
      userIds.push(userId);
    }
  }

  return userIds;
};
