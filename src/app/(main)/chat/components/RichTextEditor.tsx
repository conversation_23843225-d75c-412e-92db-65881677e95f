'use client';

import React, { useRef, useCallback, useEffect, useState } from 'react';
import styled from 'styled-components';
import { Bold, List } from 'lucide-react';
import { appTheme } from '@/app/theme';
import MentionDropdown, { MentionUser } from './MentionDropdown';
import { createMentionMarkup } from './MentionText';
import { mentionApi, mentionUtils } from '@/services/mentionService';

interface RichTextEditorProps {
  placeholder?: string;
  value?: string;
  onChange?: (htmlContent: string, textContent: string) => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  disabled?: boolean;
  maxHeight?: string;
  minHeight?: string;
  // Mention-related props
  chatId?: string;
  chatType?: 'private' | 'task' | 'department' | 'organization';
  enableMentions?: boolean;
}

const EditorContainer = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
  border: none;
  background: transparent;
`;

const ToolbarContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  border-bottom: 1px solid ${appTheme.colors.border};
  margin-bottom: ${appTheme.spacing.xs};
`;

const ToolbarButton = styled.button<{ $active?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 25px;
  border: none;
  border-radius: ${appTheme.borderRadius.sm};
  background: transparent;
  color: ${props => props.$active ? appTheme.colors.primary : appTheme.colors.text.secondary};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.$active ? appTheme.colors.primaryLight : appTheme.colors.background.lighter};
    color: ${props => props.$active ? appTheme.colors.primary : appTheme.colors.text.primary};
  }

  &:active {
    transform: scale(0.95);
  }

  /* Mobile - touch-friendly sizing */
  @media (max-width: ${appTheme.breakpoints.md}) {
    width: 32px;
    height: 32px;
  }
`;

const EditorContent = styled.div<{ $maxHeight?: string; $minHeight?: string }>`
  flex: 1;
  outline: none;
  font-size: 14px;
  font-family: inherit;
  line-height: 1.4;
  color: ${appTheme.colors.text.primary};
  max-height: ${props => props.$maxHeight || '120px'};
  min-height: ${props => props.$minHeight || '20px'};
  overflow-y: auto;
  word-wrap: break-word;
  overflow-wrap: break-word;

  &:empty::before {
    content: attr(data-placeholder);
    color: ${appTheme.colors.text.light};
    pointer-events: none;
  }

  /* Style for bold text */
  strong, b {
    font-weight: 600;
  }

  /* Style for bullet lists */
  ul {
    margin: 0;
    padding-left: 20px;
    list-style-type: disc;
  }

  li {
    margin: 2px 0;
  }

  /* Prevent nested lists for simplicity */
  ul ul {
    display: none;
  }

  /* Mobile - larger font and better touch experience */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 16px; /* Prevents zoom on iOS */
    line-height: 1.5;
    min-height: 24px;
    max-height: 100px;
  }

  /* Small mobile - optimize for smaller screens */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    max-height: 80px;
  }

  /* Enhanced scrollbar styling */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: ${appTheme.colors.border};
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: ${appTheme.colors.text.light};
  }
`;

export default function RichTextEditor({
  placeholder = 'Type your message...',
  value = '',
  onChange,
  onKeyDown,
  disabled = false,
  maxHeight,
  minHeight,
  chatId,
  chatType,
  enableMentions = false,
}: RichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isBoldActive, setIsBoldActive] = useState(false);
  const [isListActive, setIsListActive] = useState(false);

  // Mention-related state
  const [showMentionDropdown, setShowMentionDropdown] = useState(false);
  const [mentionQuery, setMentionQuery] = useState('');
  const [mentionUsers, setMentionUsers] = useState<MentionUser[]>([]);
  const [selectedMentionIndex, setSelectedMentionIndex] = useState(0);
  const [mentionPosition, setMentionPosition] = useState({ top: 0, left: 0 });
  const [mentionRange, setMentionRange] = useState<Range | null>(null);
  const [loadingMentions, setLoadingMentions] = useState(false);

  // Update editor content when value prop changes
  useEffect(() => {
    if (editorRef.current && editorRef.current.innerHTML !== value) {
      editorRef.current.innerHTML = value;
    }
  }, [value]);

  // Debounced mention search
  const debouncedMentionSearch = useCallback(
    mentionUtils.debounce(async (query: string) => {
      if (!enableMentions || !chatId || !chatType) return;

      setLoadingMentions(true);
      try {
        const response = await mentionApi.searchUsers({
          chatId,
          chatType,
          query,
          limit: 10
        });

        const sortedUsers = mentionUtils.sortUsers(response.users);
        setMentionUsers(sortedUsers);
        setSelectedMentionIndex(0);
      } catch (error) {
        console.error('Error searching mention users:', error);
        setMentionUsers([]);
      } finally {
        setLoadingMentions(false);
      }
    }, 300),
    [enableMentions, chatId, chatType]
  );

  // Check current formatting state
  const updateFormattingState = useCallback(() => {
    if (!editorRef.current) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    // Check if bold is active
    const boldActive = document.queryCommandState('bold');
    setIsBoldActive(boldActive);

    // Check if we're in a list
    const range = selection.getRangeAt(0);
    let node: Node | null = range.commonAncestorContainer;
    if (node.nodeType === Node.TEXT_NODE) {
      node = node.parentNode;
    }
    
    let listActive = false;
    let currentNode = node as Element;
    while (currentNode && currentNode !== editorRef.current) {
      if (currentNode.tagName === 'UL' || currentNode.tagName === 'LI') {
        listActive = true;
        break;
      }
      currentNode = currentNode.parentElement as Element;
    }
    setIsListActive(listActive);
  }, []);

  // Detect mention trigger and position
  const detectMentionTrigger = useCallback(() => {
    if (!enableMentions || !editorRef.current) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const textNode = range.startContainer;

    if (textNode.nodeType !== Node.TEXT_NODE) return;

    const textContent = textNode.textContent || '';
    const cursorPosition = range.startOffset;

    // Look for '@' character before cursor
    const beforeCursor = textContent.substring(0, cursorPosition);
    const mentionMatch = beforeCursor.match(/@([^@\s]*)$/);

    if (mentionMatch) {
      const query = mentionMatch[1];
      const mentionStart = cursorPosition - mentionMatch[0].length;

      // Create range for the mention text
      const mentionRange = document.createRange();
      mentionRange.setStart(textNode, mentionStart);
      mentionRange.setEnd(textNode, cursorPosition);

      // Calculate position for dropdown
      const rect = mentionRange.getBoundingClientRect();
      const editorRect = editorRef.current.getBoundingClientRect();

      setMentionQuery(query);
      setMentionRange(mentionRange);
      setMentionPosition({
        top: rect.bottom + window.scrollY + 5,
        left: rect.left + window.scrollX
      });
      setShowMentionDropdown(true);

      // Search for users
      debouncedMentionSearch(query);
    } else {
      setShowMentionDropdown(false);
      setMentionQuery('');
      setMentionRange(null);
    }
  }, [enableMentions, debouncedMentionSearch]);

  // Handle content changes
  const handleInput = useCallback(() => {
    if (!editorRef.current || !onChange) return;

    const htmlContent = editorRef.current.innerHTML;
    const textContent = editorRef.current.textContent || '';

    onChange(htmlContent, textContent);
    updateFormattingState();

    // Check for mention trigger
    detectMentionTrigger();
  }, [onChange, updateFormattingState, detectMentionTrigger]);

  // Handle mention selection
  const handleMentionSelect = useCallback((user: MentionUser) => {
    if (!mentionRange || !editorRef.current) return;

    // Create mention markup
    const mentionMarkup = createMentionMarkup({
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email
    });

    // Replace the mention trigger with the mention markup
    const selection = window.getSelection();
    if (selection) {
      selection.removeAllRanges();
      selection.addRange(mentionRange);

      // Insert the mention
      document.execCommand('insertText', false, mentionMarkup + ' ');

      // Close mention dropdown
      setShowMentionDropdown(false);
      setMentionQuery('');
      setMentionRange(null);

      // Trigger content change
      handleInput();
    }
  }, [mentionRange, handleInput]);

  // Handle mention dropdown close
  const handleMentionClose = useCallback(() => {
    setShowMentionDropdown(false);
    setMentionQuery('');
    setMentionRange(null);
  }, []);

  // Handle selection changes
  const handleSelectionChange = useCallback(() => {
    updateFormattingState();

    // Close mention dropdown if selection changes
    if (showMentionDropdown) {
      detectMentionTrigger();
    }
  }, [updateFormattingState, showMentionDropdown, detectMentionTrigger]);

  // Handle key events
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    // Handle mention dropdown navigation
    if (showMentionDropdown) {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedMentionIndex(prev =>
          prev < mentionUsers.length - 1 ? prev + 1 : 0
        );
        return;
      }

      if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedMentionIndex(prev =>
          prev > 0 ? prev - 1 : mentionUsers.length - 1
        );
        return;
      }

      if (e.key === 'Enter' || e.key === 'Tab') {
        e.preventDefault();
        if (mentionUsers[selectedMentionIndex]) {
          handleMentionSelect(mentionUsers[selectedMentionIndex]);
        }
        return;
      }

      if (e.key === 'Escape') {
        e.preventDefault();
        handleMentionClose();
        return;
      }
    }

    // Handle Enter key for list items - check this FIRST before calling onKeyDown
    if (e.key === 'Enter' && isListActive && !e.shiftKey) {
      e.preventDefault();
      e.stopPropagation(); // ป้องกันไม่ให้ event ไปถึง parent component

      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) return;

      const range = selection.getRangeAt(0);
      let listItem: Node | null = range.commonAncestorContainer;

      // Find the current list item
      while (listItem && (listItem.nodeType !== Node.ELEMENT_NODE || (listItem as Element).tagName !== 'LI')) {
        listItem = listItem.parentNode;
      }

      if (listItem && (listItem as Element).tagName === 'LI') {
        const currentLi = listItem as HTMLLIElement;
        
        // If the current list item is empty, exit the list
        if (!currentLi.textContent?.trim()) {
          const ul = currentLi.parentElement;
          if (ul && ul.tagName === 'UL') {
            // Remove the empty list item
            currentLi.remove();
            
            // Create a new paragraph after the list
            const newP = document.createElement('div');
            newP.innerHTML = '<br>';
            ul.parentNode?.insertBefore(newP, ul.nextSibling);
            
            // Move cursor to the new paragraph
            const newRange = document.createRange();
            newRange.setStart(newP, 0);
            newRange.collapse(true);
            selection.removeAllRanges();
            selection.addRange(newRange);
          }
        } else {
          // Create a new list item
          const newLi = document.createElement('li');
          newLi.innerHTML = '<br>';
          currentLi.parentNode?.insertBefore(newLi, currentLi.nextSibling);
          
          // Move cursor to the new list item
          const newRange = document.createRange();
          newRange.setStart(newLi, 0);
          newRange.collapse(true);
          selection.removeAllRanges();
          selection.addRange(newRange);
        }
        
        handleInput();
        return; // ออกจาก function เลยไม่ต้องเรียก onKeyDown
      }
    }

    // เรียก onKeyDown สำหรับ key อื่นๆ หรือเมื่อไม่ใช่ Enter ใน bullet list
    if (onKeyDown) {
      onKeyDown(e);
    }
  }, [onKeyDown, isListActive, handleInput]);

  // Format text as bold
  const toggleBold = useCallback(() => {
    if (!editorRef.current) return;
    
    editorRef.current.focus();
    document.execCommand('bold', false);
    updateFormattingState();
    handleInput();
  }, [updateFormattingState, handleInput]);

  // Toggle bullet list
  const toggleList = useCallback(() => {
    if (!editorRef.current) return;
    
    editorRef.current.focus();
    
    if (isListActive) {
      // Remove list formatting
      document.execCommand('insertHTML', false, '<div><br></div>');
    } else {
      // Add list formatting
      document.execCommand('insertUnorderedList', false);
    }
    
    updateFormattingState();
    handleInput();
  }, [isListActive, updateFormattingState, handleInput]);

  // Set up event listeners
  useEffect(() => {
    document.addEventListener('selectionchange', handleSelectionChange);
    return () => {
      document.removeEventListener('selectionchange', handleSelectionChange);
    };
  }, [handleSelectionChange]);

  return (
    <>
      <EditorContainer>
        <ToolbarContainer>
          <ToolbarButton
            type="button"
            $active={isBoldActive}
            onClick={toggleBold}
            title="Bold (Ctrl+B)"
            disabled={disabled}
          >
            <Bold size={18} />
          </ToolbarButton>
          <ToolbarButton
            type="button"
            $active={isListActive}
            onClick={toggleList}
            title="Bullet List"
            disabled={disabled}
          >
            <List size={18} />
          </ToolbarButton>
        </ToolbarContainer>

        <EditorContent
          ref={editorRef}
          contentEditable={!disabled}
          data-placeholder={placeholder}
          onInput={handleInput}
          onKeyDown={handleKeyDown}
          $maxHeight={maxHeight}
          $minHeight={minHeight}
          suppressContentEditableWarning={true}
        />
      </EditorContainer>

      {/* Mention Dropdown */}
      {showMentionDropdown && enableMentions && (
        <MentionDropdown
          users={mentionUsers}
          selectedIndex={selectedMentionIndex}
          onSelect={handleMentionSelect}
          onClose={handleMentionClose}
          position={mentionPosition}
          loading={loadingMentions}
        />
      )}
    </>
  );
}
