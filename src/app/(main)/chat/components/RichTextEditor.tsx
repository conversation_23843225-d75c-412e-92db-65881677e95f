'use client';

import React, { useRef, useCallback, useEffect, useState } from 'react';
import styled from 'styled-components';
import { Bold, List } from 'lucide-react';
import { appTheme } from '@/app/theme';

interface RichTextEditorProps {
  placeholder?: string;
  value?: string;
  onChange?: (htmlContent: string, textContent: string) => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  disabled?: boolean;
  maxHeight?: string;
  minHeight?: string;
}

const EditorContainer = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
  border: none;
  background: transparent;
`;

const ToolbarContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  padding: ${appTheme.spacing.xs} 0;
  border-bottom: 1px solid ${appTheme.colors.border};
  margin-bottom: ${appTheme.spacing.xs};
`;

const ToolbarButton = styled.button<{ $active?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  border-radius: ${appTheme.borderRadius.sm};
  background: ${props => props.$active ? appTheme.colors.primaryLight : 'transparent'};
  color: ${props => props.$active ? appTheme.colors.primary : appTheme.colors.text.secondary};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.$active ? appTheme.colors.primaryLight : appTheme.colors.background.lighter};
    color: ${props => props.$active ? appTheme.colors.primary : appTheme.colors.text.primary};
  }

  &:active {
    transform: scale(0.95);
  }

  /* Mobile - touch-friendly sizing */
  @media (max-width: ${appTheme.breakpoints.md}) {
    width: 32px;
    height: 32px;
  }
`;

const EditorContent = styled.div<{ $maxHeight?: string; $minHeight?: string }>`
  flex: 1;
  outline: none;
  font-size: 14px;
  font-family: inherit;
  line-height: 1.4;
  color: ${appTheme.colors.text.primary};
  max-height: ${props => props.$maxHeight || '120px'};
  min-height: ${props => props.$minHeight || '20px'};
  overflow-y: auto;
  word-wrap: break-word;
  overflow-wrap: break-word;

  &:empty::before {
    content: attr(data-placeholder);
    color: ${appTheme.colors.text.light};
    pointer-events: none;
  }

  /* Style for bold text */
  strong, b {
    font-weight: 600;
  }

  /* Style for bullet lists */
  ul {
    margin: 0;
    padding-left: 20px;
    list-style-type: disc;
  }

  li {
    margin: 2px 0;
  }

  /* Prevent nested lists for simplicity */
  ul ul {
    display: none;
  }

  /* Mobile - larger font and better touch experience */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 16px; /* Prevents zoom on iOS */
    line-height: 1.5;
    min-height: 24px;
    max-height: 100px;
  }

  /* Small mobile - optimize for smaller screens */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    max-height: 80px;
  }

  /* Enhanced scrollbar styling */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: ${appTheme.colors.border};
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: ${appTheme.colors.text.light};
  }
`;

export default function RichTextEditor({
  placeholder = 'Type your message...',
  value = '',
  onChange,
  onKeyDown,
  disabled = false,
  maxHeight,
  minHeight,
}: RichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isBoldActive, setIsBoldActive] = useState(false);
  const [isListActive, setIsListActive] = useState(false);

  // Update editor content when value prop changes
  useEffect(() => {
    if (editorRef.current && editorRef.current.innerHTML !== value) {
      editorRef.current.innerHTML = value;
    }
  }, [value]);

  // Check current formatting state
  const updateFormattingState = useCallback(() => {
    if (!editorRef.current) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    // Check if bold is active
    const boldActive = document.queryCommandState('bold');
    setIsBoldActive(boldActive);

    // Check if we're in a list
    const range = selection.getRangeAt(0);
    let node = range.commonAncestorContainer;
    if (node.nodeType === Node.TEXT_NODE) {
      node = node.parentNode;
    }
    
    let listActive = false;
    let currentNode = node as Element;
    while (currentNode && currentNode !== editorRef.current) {
      if (currentNode.tagName === 'UL' || currentNode.tagName === 'LI') {
        listActive = true;
        break;
      }
      currentNode = currentNode.parentElement as Element;
    }
    setIsListActive(listActive);
  }, []);

  // Handle content changes
  const handleInput = useCallback(() => {
    if (!editorRef.current || !onChange) return;

    const htmlContent = editorRef.current.innerHTML;
    const textContent = editorRef.current.textContent || '';
    
    onChange(htmlContent, textContent);
    updateFormattingState();
  }, [onChange, updateFormattingState]);

  // Handle selection changes
  const handleSelectionChange = useCallback(() => {
    updateFormattingState();
  }, [updateFormattingState]);

  // Handle key events
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (onKeyDown) {
      onKeyDown(e);
    }

    // Handle Enter key for list items
    if (e.key === 'Enter' && isListActive && !e.shiftKey) {
      e.preventDefault();
      
      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) return;

      const range = selection.getRangeAt(0);
      let listItem = range.commonAncestorContainer;
      
      // Find the current list item
      while (listItem && listItem.nodeType !== Node.ELEMENT_NODE || (listItem as Element).tagName !== 'LI') {
        listItem = listItem.parentNode;
      }

      if (listItem && (listItem as Element).tagName === 'LI') {
        const currentLi = listItem as HTMLLIElement;
        
        // If the current list item is empty, exit the list
        if (!currentLi.textContent?.trim()) {
          const ul = currentLi.parentElement;
          if (ul && ul.tagName === 'UL') {
            // Remove the empty list item
            currentLi.remove();
            
            // Create a new paragraph after the list
            const newP = document.createElement('div');
            newP.innerHTML = '<br>';
            ul.parentNode?.insertBefore(newP, ul.nextSibling);
            
            // Move cursor to the new paragraph
            const newRange = document.createRange();
            newRange.setStart(newP, 0);
            newRange.collapse(true);
            selection.removeAllRanges();
            selection.addRange(newRange);
          }
        } else {
          // Create a new list item
          const newLi = document.createElement('li');
          newLi.innerHTML = '<br>';
          currentLi.parentNode?.insertBefore(newLi, currentLi.nextSibling);
          
          // Move cursor to the new list item
          const newRange = document.createRange();
          newRange.setStart(newLi, 0);
          newRange.collapse(true);
          selection.removeAllRanges();
          selection.addRange(newRange);
        }
        
        handleInput();
      }
    }
  }, [onKeyDown, isListActive, handleInput]);

  // Format text as bold
  const toggleBold = useCallback(() => {
    if (!editorRef.current) return;
    
    editorRef.current.focus();
    document.execCommand('bold', false);
    updateFormattingState();
    handleInput();
  }, [updateFormattingState, handleInput]);

  // Toggle bullet list
  const toggleList = useCallback(() => {
    if (!editorRef.current) return;
    
    editorRef.current.focus();
    
    if (isListActive) {
      // Remove list formatting
      document.execCommand('insertHTML', false, '<div><br></div>');
    } else {
      // Add list formatting
      document.execCommand('insertUnorderedList', false);
    }
    
    updateFormattingState();
    handleInput();
  }, [isListActive, updateFormattingState, handleInput]);

  // Set up event listeners
  useEffect(() => {
    document.addEventListener('selectionchange', handleSelectionChange);
    return () => {
      document.removeEventListener('selectionchange', handleSelectionChange);
    };
  }, [handleSelectionChange]);

  return (
    <EditorContainer>
      <ToolbarContainer>
        <ToolbarButton
          type="button"
          $active={isBoldActive}
          onClick={toggleBold}
          title="Bold (Ctrl+B)"
          disabled={disabled}
        >
          <Bold size={14} />
        </ToolbarButton>
        <ToolbarButton
          type="button"
          $active={isListActive}
          onClick={toggleList}
          title="Bullet List"
          disabled={disabled}
        >
          <List size={14} />
        </ToolbarButton>
      </ToolbarContainer>
      
      <EditorContent
        ref={editorRef}
        contentEditable={!disabled}
        data-placeholder={placeholder}
        onInput={handleInput}
        onKeyDown={handleKeyDown}
        $maxHeight={maxHeight}
        $minHeight={minHeight}
        suppressContentEditableWarning={true}
      />
    </EditorContainer>
  );
}
