import { apiRequest } from '@/utils/api';


export interface MentionUser {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  imageUrl?: string;
  isOnline?: boolean;
}

export interface MentionSearchParams {
  chatId: string;
  chatType: 'private' | 'task' | 'department' | 'organization';
  query?: string;
  limit?: number;
}

// Mention API functions
export const mentionApi = {
  // Search users for mentions in a specific chat
  searchUsers: async (params: MentionSearchParams): Promise<{ users: MentionUser[] }> => {
    const searchParams = new URLSearchParams();
    
    searchParams.append('chatId', params.chatId);
    searchParams.append('chatType', params.chatType);
    
    if (params.query?.trim()) {
      searchParams.append('query', params.query.trim());
    }
    
    if (params.limit) {
      searchParams.append('limit', params.limit.toString());
    }

    const queryString = searchParams.toString();
    return apiRequest(`/mention/users${queryString ? `?${queryString}` : ''}`);
  },

  // Get user details for mention display
  getUser: async (userId: number): Promise<{ user: MentionUser }> => {
    return apiRequest(`/mention/user/${userId}`);
  },
};

// Utility functions for mention handling
export const mentionUtils = {
  // Debounce function for search input
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  },

  // Filter users based on query
  filterUsers: (users: MentionUser[], query: string): MentionUser[] => {
    if (!query.trim()) return users;
    
    const searchTerm = query.toLowerCase();
    return users.filter(user => {
      const fullName = `${user.firstName} ${user.lastName}`.toLowerCase();
      const email = user.email.toLowerCase();
      return fullName.includes(searchTerm) || email.includes(searchTerm);
    });
  },

  // Sort users by relevance (online first, then alphabetically)
  sortUsers: (users: MentionUser[]): MentionUser[] => {
    return [...users].sort((a, b) => {
      // Online users first
      if (a.isOnline && !b.isOnline) return -1;
      if (!a.isOnline && b.isOnline) return 1;
      
      // Then alphabetically by full name
      const aName = `${a.firstName} ${a.lastName}`;
      const bName = `${b.firstName} ${b.lastName}`;
      return aName.localeCompare(bName);
    });
  },

  // Get display name for user
  getDisplayName: (user: MentionUser): string => {
    return `${user.firstName} ${user.lastName}`;
  },

  // Get user initials
  getUserInitials: (user: MentionUser): string => {
    return `${user.firstName.charAt(0)}${user.lastName.charAt(0)}`.toUpperCase();
  },

  // Check if user has profile image
  hasProfileImage: (user: MentionUser): boolean => {
    return !!(user.imageUrl && user.imageUrl.trim() !== '' && 
             user.imageUrl !== 'null' && user.imageUrl !== 'undefined');
  },
};
