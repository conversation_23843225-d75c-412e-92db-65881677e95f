import { prisma } from '@/lib/prisma';
import { getMentionedUserIds } from '@/app/(main)/chat/components/MentionText';

export interface MentionNotificationData {
  messageId: number;
  chatId: number;
  chatType: string;
  chatName: string;
  senderUserId: number;
  senderName: string;
  messageContent: string;
  mentionedUserIds: number[];
}

// Mention notification service
export const mentionNotificationService = {
  // Create mention notifications for users mentioned in a message
  createMentionNotifications: async (data: MentionNotificationData) => {
    try {
      // Get or create mention notification type
      const notificationType = await prisma.notificationType.upsert({
        where: { name: 'mention' },
        update: {},
        create: {
          name: 'mention',
          displayName: 'Mention',
          description: 'User mentioned in a chat message',
          template: '{senderName} mentioned you in {chatName}',
          color: '#3b82f6',
          isActive: true,
        },
      });

      // Create notifications for each mentioned user
      const notifications = await Promise.all(
        data.mentionedUserIds.map(async (userId) => {
          // Don't notify the sender
          if (userId === data.senderUserId) return null;

          // Check if user exists and is active
          const user = await prisma.user.findUnique({
            where: { id: userId, deletedAt: null },
          });

          if (!user) return null;

          // Create notification
          const notification = await prisma.notification.create({
            data: {
              typeId: notificationType.id,
              title: `Mention in ${data.chatName}`,
              content: `${data.senderName} mentioned you in ${data.chatName}`,
              data: {
                messageId: data.messageId,
                chatId: data.chatId,
                chatType: data.chatType,
                chatName: data.chatName,
                senderUserId: data.senderUserId,
                senderName: data.senderName,
                messageContent: data.messageContent.substring(0, 200), // Truncate long messages
              },
              entityType: 'chat_message',
              entityId: data.messageId,
            },
          });

          // Create user notification
          await prisma.userNotification.create({
            data: {
              userId,
              notificationId: notification.id,
            },
          });

          return notification;
        })
      );

      return notifications.filter(Boolean);
    } catch (error) {
      console.error('Error creating mention notifications:', error);
      throw error;
    }
  },

  // Process message content and create mention notifications
  processMentionsInMessage: async (
    messageId: number,
    messageContent: string,
    chatId: number,
    chatType: string,
    chatName: string,
    senderUserId: number,
    senderName: string
  ) => {
    try {
      // Extract mentioned user IDs from message content
      const mentionedUserIds = getMentionedUserIds(messageContent);

      if (mentionedUserIds.length === 0) {
        return [];
      }

      // Create mention notifications
      return await mentionNotificationService.createMentionNotifications({
        messageId,
        chatId,
        chatType,
        chatName,
        senderUserId,
        senderName,
        messageContent,
        mentionedUserIds,
      });
    } catch (error) {
      console.error('Error processing mentions in message:', error);
      throw error;
    }
  },

  // Get mention notifications for a user
  getUserMentionNotifications: async (userId: number, limit: number = 20) => {
    try {
      const userNotifications = await prisma.userNotification.findMany({
        where: {
          userId,
          notification: {
            type: {
              name: 'mention',
            },
          },
        },
        include: {
          notification: {
            include: {
              type: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
      });

      return userNotifications;
    } catch (error) {
      console.error('Error fetching user mention notifications:', error);
      throw error;
    }
  },

  // Mark mention notification as read
  markMentionNotificationAsRead: async (userId: number, notificationId: number) => {
    try {
      const userNotification = await prisma.userNotification.updateMany({
        where: {
          userId,
          notificationId,
          isRead: false,
        },
        data: {
          isRead: true,
          readAt: new Date(),
        },
      });

      return userNotification;
    } catch (error) {
      console.error('Error marking mention notification as read:', error);
      throw error;
    }
  },

  // Get unread mention count for a user
  getUnreadMentionCount: async (userId: number) => {
    try {
      const count = await prisma.userNotification.count({
        where: {
          userId,
          isRead: false,
          notification: {
            type: {
              name: 'mention',
            },
          },
        },
      });

      return count;
    } catch (error) {
      console.error('Error getting unread mention count:', error);
      throw error;
    }
  },

  // Delete old mention notifications (cleanup)
  cleanupOldMentionNotifications: async (daysOld: number = 30) => {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      // Delete old user notifications first (due to foreign key constraints)
      await prisma.userNotification.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate,
          },
          notification: {
            type: {
              name: 'mention',
            },
          },
        },
      });

      // Delete old notifications
      const deletedNotifications = await prisma.notification.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate,
          },
          type: {
            name: 'mention',
          },
        },
      });

      return deletedNotifications;
    } catch (error) {
      console.error('Error cleaning up old mention notifications:', error);
      throw error;
    }
  },
};

// Utility functions for mention notifications
export const mentionNotificationUtils = {
  // Format notification content for display
  formatNotificationContent: (notification: any) => {
    const data = notification.data;
    return {
      title: notification.title,
      content: notification.content,
      senderName: data?.senderName || 'Someone',
      chatName: data?.chatName || 'a chat',
      messagePreview: data?.messageContent || '',
      chatId: data?.chatId,
      chatType: data?.chatType,
      messageId: data?.messageId,
      createdAt: notification.createdAt,
    };
  },

  // Check if notification is a mention
  isMentionNotification: (notification: any) => {
    return notification.type?.name === 'mention';
  },

  // Get chat URL from notification data
  getChatUrlFromNotification: (notification: any) => {
    const data = notification.data;
    if (!data?.chatId || !data?.chatType) return '/chat';
    
    return `/chat?room=${data.chatId}&type=${data.chatType}`;
  },
};
